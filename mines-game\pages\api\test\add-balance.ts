import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { userDb, initDatabase } from '@/lib/database';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { amount = 100 } = req.body;

    // Check if current balance is Infinity and fix it
    if (!isFinite(user.usdt_balance)) {
      userDb.updateBalance(user.id, 'USDT', 1000); // Reset to 1000
    }

    // Add USDT balance for testing
    userDb.addToBalance(user.id, 'USDT', amount);

    // Get updated user data
    const updatedUser = userDb.findById(user.id);

    return res.status(200).json({
      success: true,
      message: `Added ${amount} USDT to your balance for testing`,
      balance: {
        usdt: updatedUser?.usdt_balance || 0,
        ltc: updatedUser?.ltc_balance || 0
      }
    });
  } catch (error) {
    console.error('Add balance API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});
