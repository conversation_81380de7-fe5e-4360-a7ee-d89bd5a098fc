import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { GameType, GameState, BaseGameContextType } from '@/types';
import { useAuth } from './AuthContext';
import { gameFactory } from '@/lib/games/GameFactory';
import { API_ENDPOINTS } from '@/lib/utils';

interface UniversalGameContextType {
  // Current active game
  currentGame: GameState | null;
  currentGameType: GameType | null;

  // Game history
  gameHistory: GameState[];

  // Loading and error states
  loading: boolean;
  error: string | null;

  // Universal game actions
  startGame: (gameType: GameType, params: any) => Promise<boolean>;
  makeMove: (params: any) => Promise<any>;
  cashOut: () => Promise<{ success: boolean; profit: number }>;
  resetGame: () => void;
  forceResetActiveGame: () => Promise<boolean>;

  // Game management
  switchGame: (gameType: GameType) => void;
  loadGameHistory: (gameType?: GameType) => Promise<void>;

  // Utility functions
  getGameConfig: (gameType: GameType) => any;
  isGameActive: () => boolean;
  canCashOut: () => boolean;
}

const UniversalGameContext = createContext<UniversalGameContextType | undefined>(undefined);

interface UniversalGameProviderProps {
  children: ReactNode;
}

export function UniversalGameProvider({ children }: UniversalGameProviderProps) {
  const [currentGame, setCurrentGame] = useState<GameState | null>(null);
  const [currentGameType, setCurrentGameType] = useState<GameType | null>(null);
  const [gameHistory, setGameHistory] = useState<GameState[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { user } = useAuth();

  // Initialize game factory and load active game when user changes
  useEffect(() => {
    if (user) {
      initializeAndLoadGame();
    } else {
      resetGame();
    }
  }, [user]);

  /**
   * Initialize game factory and load any active game
   */
  const initializeAndLoadGame = async () => {
    try {
      setLoading(true);
      setError(null);

      // Ensure game factory is initialized
      await gameFactory.initialize();

      // Load any active game
      await loadActiveGame();

    } catch (err) {
      console.error('Failed to initialize games:', err);
      setError('Failed to initialize games');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load active game from server
   */
  const loadActiveGame = async () => {
    try {
      const response = await fetch('/api/game/active', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.game) {
          setCurrentGame(data.game);
          setCurrentGameType(data.game.game_type);
        }
      }
    } catch (err) {
      console.error('Failed to load active game:', err);
    }
  };

  /**
   * Start a new game
   */
  const startGame = async (gameType: GameType, params: any): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      // Ensure we have the game type set
      if (currentGameType !== gameType) {
        setCurrentGameType(gameType);
      }

      const requestBody = {
        game_type: gameType,
        ...params
      };
      console.log('🌍 Frontend sending request:', JSON.stringify(requestBody, null, 2));

      const response = await fetch('/api/game/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (data.success && data.game) {
        console.log('🌍 UniversalGameContext: Game started successfully');
        setCurrentGame(data.game);

        // For crash games, add extra logging to help debug state issues
        if (gameType === 'crash') {
          console.log('🌍 UniversalGameContext: Crash game created:', {
            id: data.game.id,
            bet_amount: data.game.bet_amount,
            auto_cash_out: data.game.auto_cash_out,
            status: data.game.status,
            phase: data.game.phase
          });
        }

        return true;
      } else {
        console.error('Game start failed:', data.error);
        setError(data.error || 'Failed to start game');
        return false;
      }
    } catch (err) {
      console.error('Start game error:', err);
      setError('Failed to start game');
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Make a move in the current game
   */
  const makeMove = async (params: any): Promise<any> => {
    console.log('🎯 UniversalGameContext: makeMove called with params:', params);
    console.log('🎯 UniversalGameContext: currentGame:', currentGame);
    console.log('🎯 UniversalGameContext: currentGameType:', currentGameType);

    // First try to reload active game if we don't have one
    if (!currentGame || !currentGameType) {
      console.log('🎯 UniversalGameContext: No current game, attempting to reload...');
      await loadActiveGame();

      // If still no game after reload, this might be a timing issue
      if (!currentGame || !currentGameType) {
        console.warn('🎯 UniversalGameContext: No active game found after reload');
        // Return a failure result instead of throwing
        return {
          success: false,
          hit: true, // Treat as loss
          gameOver: true,
          error: 'No active game'
        };
      }
    }

    try {
      setLoading(true);
      setError(null);

      console.log('🎯 UniversalGameContext: Making API call to /api/game/move');
      const response = await fetch('/api/game/move', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          game_id: currentGame.id,
          game_type: currentGameType,
          ...params
        }),
      });

      const data = await response.json();
      console.log('🎯 UniversalGameContext: API response:', data);

      if (data.success) {
        // Update current game state
        if (data.gameState) {
          setCurrentGame(data.gameState);
        }

        // If game is over, refresh history and clear current game
        if (data.gameOver) {
          setTimeout(() => {
            loadGameHistory(currentGameType);
            if (data.gameState?.status !== 'active') {
              setCurrentGame(null);
            }
          }, 2000);
        }

        return data;
      } else {
        console.error('🎯 UniversalGameContext: Move failed:', data.error);
        setError(data.error || 'Move failed');

        // Return failure result instead of throwing
        return {
          success: false,
          hit: true, // Treat as loss
          gameOver: true,
          error: data.error || 'Move failed'
        };
      }
    } catch (err) {
      console.error('🎯 UniversalGameContext: Make move error:', err);
      setError(err instanceof Error ? err.message : 'Move failed');

      // Return failure result instead of throwing
      return {
        success: false,
        hit: true, // Treat as loss
        gameOver: true,
        error: err instanceof Error ? err.message : 'Move failed'
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Cash out current game
   */
  const cashOut = async (): Promise<{ success: boolean; profit: number }> => {
    if (!currentGame) {
      throw new Error('No active game');
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/game/cashout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          game_id: currentGame.id,
          game_type: currentGameType
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Update game state
        if (data.gameState) {
          setCurrentGame(data.gameState);
        }

        // Refresh history and clear current game
        setTimeout(() => {
          if (currentGameType) {
            loadGameHistory(currentGameType);
          }
          setCurrentGame(null);
        }, 2000);

        return {
          success: true,
          profit: data.profit || 0
        };
      } else {
        setError(data.error || 'Cash out failed');
        return {
          success: false,
          profit: 0
        };
      }
    } catch (err) {
      console.error('Cash out error:', err);
      setError('Cash out failed');
      return {
        success: false,
        profit: 0
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Reset current game state
   */
  const resetGame = () => {
    setCurrentGame(null);
    setCurrentGameType(null);
    setGameHistory([]);
    setError(null);
  };

  /**
   * Force reset any active games (cancels active game and refunds bet)
   */
  const forceResetActiveGame = async (): Promise<boolean> => {
    try {
      setLoading(true);
      const response = await fetch('/api/game/reset-active', {
        method: 'POST',
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          resetGame();
          await loadActiveGame(); // Reload to confirm reset
          return true;
        }
      }
      return false;
    } catch (err) {
      console.error('Failed to reset active game:', err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Switch to a different game type
   */
  const switchGame = (gameType: GameType) => {
    if (currentGame?.status === 'active') {
      console.warn('Cannot switch games while a game is active');
      return;
    }

    setCurrentGameType(gameType);
    setCurrentGame(null);
    setError(null);

    // Load history for the new game type
    loadGameHistory(gameType);
  };

  /**
   * Load game history
   */
  const loadGameHistory = async (gameType?: GameType) => {
    try {
      const typeParam = gameType || currentGameType;
      const url = typeParam
        ? `/api/game/history?game_type=${typeParam}`
        : '/api/game/history';

      const response = await fetch(url, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.games) {
          setGameHistory(data.games);
        }
      }
    } catch (err) {
      console.error('Failed to load game history:', err);
    }
  };

  /**
   * Get game configuration
   */
  const getGameConfig = (gameType: GameType) => {
    return gameFactory.getGameConfig(gameType);
  };

  /**
   * Check if current game is active
   */
  const isGameActive = (): boolean => {
    return currentGame?.status === 'active';
  };

  /**
   * Check if player can cash out
   */
  const canCashOut = (): boolean => {
    if (!currentGame || !currentGameType) return false;

    const provider = gameFactory.getGameProvider(currentGameType);
    if (!provider) return false;

    // For mines game, check if any cells are revealed
    if (currentGameType === 'mines') {
      return isGameActive() && (currentGame as any).revealed_cells?.length > 0;
    }

    // Default: can cash out if game is active and has positive multiplier
    return isGameActive() && currentGame.current_multiplier > 1;
  };

  const value: UniversalGameContextType = {
    currentGame,
    currentGameType,
    gameHistory,
    loading,
    error,
    startGame,
    makeMove,
    cashOut,
    resetGame,
    forceResetActiveGame,
    switchGame,
    loadGameHistory,
    getGameConfig,
    isGameActive,
    canCashOut
  };

  return (
    <UniversalGameContext.Provider value={value}>
      {children}
    </UniversalGameContext.Provider>
  );
}

export function useUniversalGame() {
  const context = useContext(UniversalGameContext);
  if (context === undefined) {
    throw new Error('useUniversalGame must be used within a UniversalGameProvider');
  }
  return context;
}
