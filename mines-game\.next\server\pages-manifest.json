{"/_app": "pages/_app.js", "/_error": "pages/_error.js", "/_document": "pages/_document.js", "/api/auth/me": "pages/api/auth/me.js", "/": "pages/index.js", "/lobby": "pages/lobby.js", "/api/lobby/stats": "pages/api/lobby/stats.js", "/api/game/list": "pages/api/game/list.js", "/api/game/active": "pages/api/game/active.js", "/api/test/add-balance": "pages/api/test/add-balance.js", "/game/[gameType]": "pages/game/[gameType].js", "/api/game/move": "pages/api/game/move.js", "/api/game/cashout": "pages/api/game/cashout.js", "/api/game/history": "pages/api/game/history.js", "/api/game/reset-active": "pages/api/game/reset-active.js", "/api/game/start": "pages/api/game/start.js"}